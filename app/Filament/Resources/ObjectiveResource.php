<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ObjectiveResource\Pages;
use App\Filament\Resources\ObjectiveResource\RelationManagers;
use App\Models\Objective;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

use Filament\Support\Enums\FontWeight;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;

class ObjectiveResource extends Resource
{
    protected static ?string $model = Objective::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?string $navigationGroup = 'OKR Management';

    protected static ?string $navigationLabel = 'Objectives';

    protected static ?string $modelLabel = 'Objective';

    protected static ?string $pluralModelLabel = 'Objectives';

    protected static ?int $navigationSort = 1;

    // Access control
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return in_array($user->role, ['admin', 'supervisor']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Objective')
                    ->schema([
                        Forms\Components\TextInput::make('nama_objective')
                            ->label('Nama Objective')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\RichEditor::make('deskripsi')
                            ->label('Deskripsi')
                            ->columnSpanFull(),

                        Forms\Components\Select::make('okr_period_id')
                            ->label('OKR Period')
                            ->options(function () {
                                return \App\Models\OkrPeriod::all()
                                    ->mapWithKeys(function ($period) {
                                        $label = $period->nama_periode;
                                        if ($period->tanggal_mulai && $period->tanggal_selesai) {
                                            $label .= ' (' . $period->tanggal_mulai->format('d M Y') . ' - ' . $period->tanggal_selesai->format('d M Y') . ')';
                                        }
                                        return [$period->id => $label];
                                    });
                            })
                            ->searchable()
                            ->preload()
                            ->required()
                            ->default(function () {
                                $activePeriod = \App\Models\OkrPeriod::where('is_active', true)->first();
                                return $activePeriod?->id;
                            })
                            ->live()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state) {
                                    $period = \App\Models\OkrPeriod::find($state);
                                    if ($period) {
                                        $set('periode_mulai', $period->tanggal_mulai->format('Y-m-d'));
                                        $set('periode_selesai', $period->tanggal_selesai->format('Y-m-d'));
                                    }
                                }
                            })
                            ->helperText('Pilih periode OKR yang sesuai untuk objective ini')
                            ->columnSpanFull(),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('periode_mulai')
                                    ->label('Periode Mulai')
                                    ->required()
                                    ->readOnly()
                                    ->dehydrated()
                                    ->default(function () {
                                        $activePeriod = \App\Models\OkrPeriod::where('is_active', true)->first();
                                        return $activePeriod?->tanggal_mulai?->format('Y-m-d');
                                    })
                                    ->helperText('Otomatis diisi berdasarkan OKR Period yang dipilih'),

                                Forms\Components\DatePicker::make('periode_selesai')
                                    ->label('Periode Selesai')
                                    ->required()
                                    ->readOnly()
                                    ->dehydrated()
                                    ->default(function () {
                                        $activePeriod = \App\Models\OkrPeriod::where('is_active', true)->first();
                                        return $activePeriod?->tanggal_selesai?->format('Y-m-d');
                                    })
                                    ->helperText('Otomatis diisi berdasarkan OKR Period yang dipilih'),
                            ]),

                        Forms\Components\DatePicker::make('target_completion')
                            ->label('Target Penyelesaian')
                            ->after('periode_mulai')
                            ->before('periode_selesai'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Assignment & Status')
                    ->schema([
                        Forms\Components\Select::make('entitas_id')
                            ->label('Entitas')
                            ->options(\App\Models\Entitas::pluck('nama', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set) {
                                // Reset dependent fields when entitas changes
                                $set('departemen_id', null);
                                $set('divisi_id', null);
                                $set('owner_id', null);
                            })
                            ->dehydrated()
                            ->helperText('Pilih entitas terlebih dahulu'),

                        Forms\Components\Select::make('departemen_id')
                            ->label('Departemen')
                            ->options(function (callable $get) {
                                $entitasId = $get('entitas_id');
                                if (!$entitasId) {
                                    return [];
                                }
                                // Get departemen yang memiliki karyawan di entitas yang dipilih
                                return \App\Models\Departemen::whereHas('karyawan', function ($query) use ($entitasId) {
                                    $query->where('id_entitas', $entitasId);
                                })->pluck('nama_departemen', 'id');
                            })
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->disabled(fn(callable $get) => !$get('entitas_id'))
                            ->placeholder(fn(callable $get) => $get('entitas_id') ? 'Pilih departemen...' : 'Pilih entitas terlebih dahulu')
                            ->afterStateUpdated(function (callable $set) {
                                // Reset dependent fields when departemen changes
                                $set('divisi_id', null);
                                $set('owner_id', null);
                            })
                            ->dehydrated()
                            ->helperText('Departemen akan muncul setelah memilih entitas'),

                        Forms\Components\Select::make('divisi_id')
                            ->label('Divisi')
                            ->options(function (callable $get) {
                                $departemenId = $get('departemen_id');
                                $entitasId = $get('entitas_id');
                                if (!$departemenId || !$entitasId) {
                                    return [];
                                }
                                // Get divisi yang memiliki karyawan di departemen dan entitas yang dipilih
                                return \App\Models\Divisi::where('departemen_id', $departemenId)
                                    ->whereHas('karyawan', function ($query) use ($entitasId) {
                                        $query->where('id_entitas', $entitasId);
                                    })
                                    ->pluck('nama_divisi', 'id');
                            })
                            ->searchable()
                            ->disabled(fn(callable $get) => !$get('departemen_id'))
                            ->placeholder(function (callable $get) {
                                if (!$get('entitas_id')) return 'Pilih entitas terlebih dahulu';
                                if (!$get('departemen_id')) return 'Pilih departemen terlebih dahulu';
                                return 'Pilih divisi...';
                            })
                            ->helperText('Divisi akan muncul setelah memilih departemen')
                            ->dehydrated(),

                        Forms\Components\Select::make('owner_id')
                            ->label('Pemilik Objective')
                            ->options(function (callable $get) {
                                $departemenId = $get('departemen_id');
                                $entitasId = $get('entitas_id');
                                if (!$departemenId || !$entitasId) {
                                    return [];
                                }
                                // Get users yang memiliki karyawan di departemen dan entitas yang dipilih
                                return \App\Models\User::whereHas('karyawan', function ($query) use ($departemenId, $entitasId) {
                                    $query->where('id_departemen', $departemenId)
                                        ->where('id_entitas', $entitasId);
                                })
                                    ->with('karyawan')
                                    ->get()
                                    ->mapWithKeys(function ($user) {
                                        $label = $user->name;
                                        if ($user->karyawan) {
                                            $label .= ' (' . $user->karyawan->nama_lengkap . ')';
                                        }
                                        return [$user->id => $label];
                                    });
                            })
                            ->searchable()
                            ->required()
                            ->disabled(fn(callable $get) => !$get('departemen_id'))
                            ->placeholder(function (callable $get) {
                                if (!$get('entitas_id')) return 'Pilih entitas terlebih dahulu';
                                if (!$get('departemen_id')) return 'Pilih departemen terlebih dahulu';
                                return 'Pilih pemilik objective...';
                            })
                            ->helperText('Pemilik objective akan muncul setelah memilih departemen'),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'draft' => 'Draft',
                                'active' => 'Aktif',
                                'completed' => 'Selesai',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->required()
                            ->default('draft')
                            ->native(false),

                        Forms\Components\TextInput::make('progress_percentage')
                            ->label('Progress (%)')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->default(0)
                            ->suffix('%')
                            ->disabled(fn(string $operation): bool => $operation === 'create'),
                    ])
                    ->columns(2),

                // Key Results Section
                Forms\Components\Section::make('Key Results')
                    ->description('Definisikan hasil kunci yang terukur untuk objective ini. Key Results harus spesifik, terukur, dan memiliki target yang jelas.')
                    ->schema([
                        Forms\Components\Repeater::make('keyResults')
                            ->relationship('keyResults')
                            ->schema([
                                Forms\Components\TextInput::make('nama_key_result')
                                    ->label('Nama Key Result')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpanFull(),

                                Forms\Components\Textarea::make('deskripsi')
                                    ->label('Deskripsi')
                                    ->rows(2)
                                    ->columnSpanFull(),

                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Select::make('tipe_metrik')
                                            ->label('Tipe Metrik')
                                            ->options([
                                                'number' => 'Angka',
                                                'percentage' => 'Persentase',
                                                'currency' => 'Mata Uang',
                                                'boolean' => 'Ya/Tidak',
                                            ])
                                            ->required()
                                            ->native(false)
                                            ->live(),

                                        Forms\Components\TextInput::make('target_value')
                                            ->label('Target Value')
                                            ->numeric()
                                            ->required()
                                            ->step(0.01),

                                        Forms\Components\TextInput::make('unit_measurement')
                                            ->label('Unit Pengukuran')
                                            ->placeholder('contoh: unit, orang, rupiah')
                                            ->visible(fn(callable $get) => in_array($get('tipe_metrik'), ['number'])),
                                    ]),

                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('current_value')
                                            ->label('Current Value')
                                            ->numeric()
                                            ->default(0)
                                            ->step(0.01),

                                        Forms\Components\TextInput::make('weight')
                                            ->label('Bobot (%)')
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(100)
                                            ->default(25)
                                            ->suffix('%'),

                                        Forms\Components\DatePicker::make('due_date')
                                            ->label('Target Tanggal'),
                                    ]),

                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'not_started' => 'Belum Dimulai',
                                        'in_progress' => 'Sedang Berjalan',
                                        'completed' => 'Selesai',
                                        'at_risk' => 'Berisiko',
                                    ])
                                    ->default('not_started')
                                    ->native(false),

                                Forms\Components\Hidden::make('created_by')
                                    ->default(Auth::id()),
                            ])
                            ->columns(1)
                            ->collapsible()
                            ->cloneable()
                            ->reorderable()
                            ->defaultItems(0)
                            ->addActionLabel('Tambah Key Result')
                            ->deleteAction(
                                fn($action) => $action->requiresConfirmation()
                            )
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                // Calculate total weight
                                $totalWeight = collect($state)->sum('weight');
                                if ($totalWeight > 100) {
                                    \Filament\Notifications\Notification::make()
                                        ->title('Peringatan')
                                        ->body("Total bobot Key Results adalah {$totalWeight}%. Disarankan tidak melebihi 100%.")
                                        ->warning()
                                        ->send();
                                }
                            }),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                // Tactics Section
                Forms\Components\Section::make('Tactics')
                    ->description('Strategi dan taktik untuk mencapai objective. Tactics adalah pendekatan strategis yang akan digunakan untuk mencapai Key Results.')
                    ->schema([
                        Forms\Components\Repeater::make('tactics')
                            ->relationship('tactics')
                            ->schema([
                                Forms\Components\TextInput::make('nama_tactic')
                                    ->label('Nama Tactic')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpanFull(),

                                Forms\Components\Textarea::make('deskripsi')
                                    ->label('Deskripsi')
                                    ->rows(2)
                                    ->columnSpanFull(),

                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\Select::make('jenis_tactic')
                                            ->label('Jenis Tactic')
                                            ->options([
                                                'strategic' => 'Strategis',
                                                'operational' => 'Operasional',
                                                'tactical' => 'Taktis',
                                                'support' => 'Pendukung',
                                            ])
                                            ->required()
                                            ->native(false),

                                        Forms\Components\Select::make('priority')
                                            ->label('Prioritas')
                                            ->options([
                                                'low' => 'Rendah',
                                                'medium' => 'Sedang',
                                                'high' => 'Tinggi',
                                                'critical' => 'Kritis',
                                            ])
                                            ->required()
                                            ->default('medium')
                                            ->native(false),

                                        Forms\Components\Select::make('status')
                                            ->label('Status')
                                            ->options([
                                                'planned' => 'Direncanakan',
                                                'in_progress' => 'Sedang Berjalan',
                                                'completed' => 'Selesai',
                                                'blocked' => 'Terblokir',
                                            ])
                                            ->default('planned')
                                            ->native(false),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\DatePicker::make('tanggal_mulai')
                                            ->label('Tanggal Mulai'),

                                        Forms\Components\DatePicker::make('tanggal_target')
                                            ->label('Target Selesai'),
                                    ]),

                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('estimasi_effort')
                                            ->label('Estimasi Effort (jam)')
                                            ->numeric()
                                            ->minValue(1)
                                            ->suffix('jam'),

                                        Forms\Components\TextInput::make('skor_dampak')
                                            ->label('Skor Dampak (1-10)')
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(10)
                                            ->default(5),

                                        Forms\Components\TextInput::make('progress_percentage')
                                            ->label('Progress (%)')
                                            ->numeric()
                                            ->minValue(0)
                                            ->maxValue(100)
                                            ->default(0)
                                            ->suffix('%'),
                                    ]),

                                Forms\Components\TextInput::make('pemilik')
                                    ->label('Pemilik/PIC')
                                    ->maxLength(255),

                                Forms\Components\Textarea::make('sumber_daya')
                                    ->label('Sumber Daya yang Dibutuhkan')
                                    ->rows(2),

                                Forms\Components\Textarea::make('kriteria_keberhasilan')
                                    ->label('Kriteria Keberhasilan')
                                    ->rows(2),

                                Forms\Components\Textarea::make('dependensi')
                                    ->label('Dependensi')
                                    ->rows(2)
                                    ->helperText('Tactic lain atau faktor eksternal yang mempengaruhi'),

                                Forms\Components\Hidden::make('created_by')
                                    ->default(Auth::id()),
                            ])
                            ->columns(1)
                            ->collapsible()
                            ->cloneable()
                            ->reorderable()
                            ->defaultItems(0)
                            ->addActionLabel('Tambah Tactic')
                            ->deleteAction(
                                fn($action) => $action->requiresConfirmation()
                            ),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                // Tasks Section - Create new tasks directly
                Forms\Components\Section::make('Tasks')
                    ->description('Buat task-task baru yang terkait dengan objective ini')
                    ->schema([
                        Forms\Components\Repeater::make('new_tasks')
                            ->label('Tasks')
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Nama Task')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpanFull(),

                                Forms\Components\Textarea::make('description')
                                    ->label('Deskripsi Task')
                                    ->rows(3)
                                    ->columnSpanFull(),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\Select::make('project_id')
                                            ->label('Project')
                                            ->options(\App\Models\Project::pluck('name', 'id'))
                                            ->searchable()
                                            ->preload()
                                            ->createOptionForm([
                                                Forms\Components\TextInput::make('name')
                                                    ->label('Nama Project')
                                                    ->required()
                                                    ->maxLength(255),
                                                Forms\Components\Textarea::make('description')
                                                    ->label('Deskripsi Project')
                                                    ->rows(3),
                                                Forms\Components\Select::make('status')
                                                    ->label('Status')
                                                    ->options([
                                                        'planning' => 'Planning',
                                                        'active' => 'Active',
                                                        'completed' => 'Completed',
                                                        'on_hold' => 'On Hold',
                                                    ])
                                                    ->default('planning')
                                                    ->required(),
                                                Forms\Components\DatePicker::make('start_date')
                                                    ->label('Tanggal Mulai')
                                                    ->default(now()),
                                                Forms\Components\DatePicker::make('end_date')
                                                    ->label('Tanggal Selesai')
                                                    ->default(now()->addMonths(3)),
                                            ])
                                            ->createOptionUsing(function (array $data): int {
                                                $data['created_by'] = auth()->id();
                                                return \App\Models\Project::create($data)->getKey();
                                            }),

                                        Forms\Components\Select::make('assigned_to')
                                            ->label('Assigned To')
                                            ->options(\App\Models\User::pluck('name', 'id'))
                                            ->searchable()
                                            ->preload(),
                                    ]),

                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\DatePicker::make('start_date')
                                            ->label('Tanggal Mulai')
                                            ->default(now()),

                                        Forms\Components\DatePicker::make('due_date')
                                            ->label('Tanggal Target')
                                            ->default(now()->addWeeks(2)),

                                        Forms\Components\Select::make('status')
                                            ->label('Status')
                                            ->options([
                                                'todo' => 'To Do',
                                                'in_progress' => 'In Progress',
                                                'completed' => 'Completed',
                                            ])
                                            ->default('todo')
                                            ->required()
                                            ->native(false),
                                    ]),

                                Forms\Components\TextInput::make('contribution_percentage')
                                    ->label('Kontribusi (%)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(100)
                                    ->default(100)
                                    ->suffix('%')
                                    ->helperText('Seberapa besar kontribusi task ini terhadap objective')
                                    ->required(),
                            ])
                            ->columns(1)
                            ->collapsible()
                            ->cloneable()
                            ->reorderable()
                            ->defaultItems(0)
                            ->addActionLabel('Tambah Task Baru')
                            ->deleteAction(
                                fn($action) => $action->requiresConfirmation()
                            ),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                // Existing Tasks Connection Section (Optional)
                Forms\Components\Section::make('Hubungkan Task yang Sudah Ada')
                    ->description('Opsional: Hubungkan task yang sudah ada dengan objective ini')
                    ->schema([
                        Forms\Components\Repeater::make('existing_task_connections')
                            ->label('Task Connections')
                            ->schema([
                                Forms\Components\Select::make('task_id')
                                    ->label('Pilih Task')
                                    ->options(function () {
                                        return \App\Models\Task::with(['project', 'assignedUser'])
                                            ->get()
                                            ->mapWithKeys(function ($task) {
                                                $label = $task->name;
                                                if ($task->project) {
                                                    $label .= ' (' . $task->project->name . ')';
                                                }
                                                if ($task->assignedUser) {
                                                    $label .= ' - ' . $task->assignedUser->name;
                                                }
                                                return [$task->id => $label];
                                            });
                                    })
                                    ->searchable()
                                    ->required()
                                    ->distinct()
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('contribution_percentage')
                                    ->label('Kontribusi (%)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(100)
                                    ->default(100)
                                    ->suffix('%')
                                    ->helperText('Seberapa besar kontribusi task ini terhadap objective')
                                    ->required(),
                            ])
                            ->columns(2)
                            ->collapsible()
                            ->cloneable()
                            ->reorderable()
                            ->defaultItems(0)
                            ->addActionLabel('Hubungkan Task Existing')
                            ->deleteAction(
                                fn($action) => $action->requiresConfirmation()
                            ),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->hiddenOn('create'),

                Forms\Components\Hidden::make('created_by')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_objective')
                    ->label('Nama Objective')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium)
                    ->wrap(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'draft' => 'gray',
                        'active' => 'success',
                        'completed' => 'primary',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'active' => 'Aktif',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->formatStateUsing(fn($state) => $state . '%')
                    ->color(fn($state) => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'info',
                        default => 'danger',
                    })
                    ->badge(),

                Tables\Columns\TextColumn::make('owner.name')
                    ->label('Pemilik')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('entitas.nama')
                    ->label('Entitas')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('departemen.nama_departemen')
                    ->label('Departemen')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('divisi.nama_divisi')
                    ->label('Divisi')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('periode_mulai')
                    ->label('Periode Mulai')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('periode_selesai')
                    ->label('Periode Selesai')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('target_completion')
                    ->label('Target Selesai')
                    ->date('d M Y')
                    ->sortable()
                    ->toggleable()
                    ->color(fn($record) => $record->is_overdue ? 'danger' : 'primary'),

                Tables\Columns\TextColumn::make('key_results_count')
                    ->label('Key Results')
                    ->counts('keyResults')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('tactics_count')
                    ->label('Tactics')
                    ->counts('tactics')
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'active' => 'Aktif',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                    ])
                    ->multiple(),

                SelectFilter::make('owner_id')
                    ->label('Pemilik')
                    ->relationship('owner', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                SelectFilter::make('entitas_id')
                    ->label('Entitas')
                    ->relationship('entitas', 'nama')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                SelectFilter::make('departemen_id')
                    ->label('Departemen')
                    ->relationship('departemen', 'nama_departemen')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Tables\Filters\Filter::make('progress_range')
                    ->label('Progress Range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('progress_from')
                                    ->label('From (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100),
                                Forms\Components\TextInput::make('progress_to')
                                    ->label('To (%)')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(100),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['progress_from'],
                                fn(Builder $query, $value): Builder => $query->where('progress_percentage', '>=', $value),
                            )
                            ->when(
                                $data['progress_to'],
                                fn(Builder $query, $value): Builder => $query->where('progress_percentage', '<=', $value),
                            );
                    }),

                Tables\Filters\Filter::make('date_range')
                    ->label('Period Range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('periode_from')
                                    ->label('From Date'),
                                Forms\Components\DatePicker::make('periode_to')
                                    ->label('To Date'),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['periode_from'],
                                fn(Builder $query, $date): Builder => $query->where('periode_mulai', '>=', $date),
                            )
                            ->when(
                                $data['periode_to'],
                                fn(Builder $query, $date): Builder => $query->where('periode_selesai', '<=', $date),
                            );
                    }),

                Tables\Filters\TernaryFilter::make('is_overdue')
                    ->label('Overdue Status')
                    ->placeholder('All objectives')
                    ->trueLabel('Overdue only')
                    ->falseLabel('Not overdue')
                    ->queries(
                        true: fn(Builder $query) => $query->where('target_completion', '<', now())->where('status', '!=', 'completed'),
                        false: fn(Builder $query) => $query->where(function ($q) {
                            $q->where('target_completion', '>=', now())
                                ->orWhere('status', 'completed')
                                ->orWhereNull('target_completion');
                        }),
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat'),
                Tables\Actions\EditAction::make()
                    ->label('Edit'),
                Tables\Actions\DeleteAction::make()
                    ->label('Hapus'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih'),
                ]),
            ])
            ->defaultSort('objectives.created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    public static function getRelations(): array
    {
        // Relation managers tersedia sebagai tabs di view/edit page
        // Form terintegrasi tersedia di main form, relation managers sebagai alternative view
        return [
            RelationManagers\KeyResultsRelationManager::class,
            RelationManagers\TacticsRelationManager::class,
            RelationManagers\TasksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListObjectives::route('/'),
            'create' => Pages\CreateObjective::route('/create'),
            'view' => Pages\ViewObjective::route('/{record}'),
            'edit' => Pages\EditObjective::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount(['keyResults', 'tactics'])
            ->with(['owner:id,name', 'entitas:id,nama', 'departemen:id,nama_departemen', 'divisi:id,nama_divisi']);
    }
}
