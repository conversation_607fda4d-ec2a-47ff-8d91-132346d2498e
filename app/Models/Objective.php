<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

use App\Traits\SafeDateOperations;

class Objective extends Model
{
    use HasFactory, SoftDeletes, SafeDateOperations;

    protected $fillable = [
        'okr_period_id',
        'nama_objective',
        'deskripsi',
        'periode_mulai',
        'periode_selesai',
        'status',
        'progress_percentage',
        'target_completion',
        'actual_completion',
        'owner_id',
        'entitas_id',
        'departemen_id',
        'divisi_id',
        'created_by',
    ];

    protected $casts = [
        'periode_mulai' => 'date',
        'periode_selesai' => 'date',
        'target_completion' => 'date',
        'actual_completion' => 'date',
        'progress_percentage' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Relationships
    public function okrPeriod(): BelongsTo
    {
        return $this->belongsTo(OkrPeriod::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(OkrDocument::class, 'documentable');
    }

    public function tasks(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'objective_tasks')
            ->withPivot('contribution_percentage')
            ->withTimestamps();
    }

    public function departemen(): BelongsTo
    {
        return $this->belongsTo(Departemen::class);
    }

    public function divisi(): BelongsTo
    {
        return $this->belongsTo(Divisi::class);
    }

    public function entitas(): BelongsTo
    {
        return $this->belongsTo(Entitas::class);
    }

    public function keyResults(): HasMany
    {
        return $this->hasMany(KeyResult::class);
    }

    public function tactics(): HasMany
    {
        return $this->hasMany(Tactic::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('periode_mulai', [$startDate, $endDate])
            ->orWhereBetween('periode_selesai', [$startDate, $endDate]);
    }

    public function scopeByOwner($query, $userId)
    {
        return $query->where('owner_id', $userId);
    }

    public function scopeByDepartemen($query, $departemenId)
    {
        return $query->where('departemen_id', $departemenId);
    }

    public function scopeByDivisi($query, $divisiId)
    {
        return $query->where('divisi_id', $divisiId);
    }

    public function scopeWithValidDepartemen($query)
    {
        return $query->whereNotNull('departemen_id');
    }

    public function scopeWithValidDivisi($query)
    {
        return $query->whereNotNull('divisi_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($objective) {
            if (!$objective->created_by) {
                $objective->created_by = auth()->id();
            }

            // Auto-fill periode_mulai and periode_selesai from OKR Period if not set
            if ($objective->okr_period_id && (!$objective->periode_mulai || !$objective->periode_selesai)) {
                $period = OkrPeriod::find($objective->okr_period_id);
                if ($period) {
                    if (!$objective->periode_mulai) {
                        $objective->periode_mulai = $period->tanggal_mulai;
                    }
                    if (!$objective->periode_selesai) {
                        $objective->periode_selesai = $period->tanggal_selesai;
                    }
                }
            }
        });
    }

    public function scopeWithProgress($query)
    {
        return $query->withCount(['keyResults', 'tactics'])
            ->withAvg('keyResults', 'progress_percentage')
            ->withAvg('tactics', 'progress_percentage');
    }

    // Accessors & Mutators
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'Draft',
            'active' => 'Aktif',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
            default => 'Unknown',
        };
    }

    public function getProgressColorAttribute(): string
    {
        return match (true) {
            $this->progress_percentage >= 80 => 'success',
            $this->progress_percentage >= 60 => 'warning',
            $this->progress_percentage >= 40 => 'info',
            default => 'danger',
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->target_completion &&
            $this->target_completion->isPast() &&
            $this->status !== 'completed';
    }

    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->target_completion || $this->status === 'completed') {
            return null;
        }

        return now()->diffInDays($this->target_completion, false);
    }

    // Methods
    public function calculateProgress(): int
    {
        $keyResults = $this->keyResults;

        if ($keyResults->isEmpty()) {
            $this->update(['progress_percentage' => 0]);
            return 0;
        }

        // Calculate weighted average if key results have different weights
        $totalWeight = $keyResults->sum('weight') ?: $keyResults->count();
        $weightedProgress = $keyResults->sum(function ($kr) {
            return $kr->progress_percentage * ($kr->weight ?: 1);
        });

        $calculatedProgress = round($weightedProgress / $totalWeight);

        // Auto-update status based on progress
        $this->updateStatusBasedOnProgress($calculatedProgress);

        $this->update(['progress_percentage' => $calculatedProgress]);

        return $calculatedProgress;
    }

    protected function updateStatusBasedOnProgress(int $progress): void
    {
        $newStatus = match (true) {
            $progress >= 100 => 'completed',
            $progress >= 1 && $this->status === 'draft' => 'active',
            $progress === 0 && $this->status === 'active' => 'draft',
            default => $this->status
        };

        if ($newStatus !== $this->status) {
            $this->update([
                'status' => $newStatus,
                'actual_completion' => $newStatus === 'completed' ? now()->toDateString() : null
            ]);
        }
    }

    public function updateStatus(): void
    {
        if ($this->progress_percentage >= 100) {
            $this->update([
                'status' => 'completed',
                'actual_completion' => now()->toDateString()
            ]);
        } elseif ($this->progress_percentage > 0 && $this->status === 'draft') {
            $this->update(['status' => 'active']);
        }
    }

    public function getCompletionRate(): array
    {
        $totalKeyResults = $this->keyResults()->count();
        $completedKeyResults = $this->keyResults()->where('status', 'completed')->count();

        $totalTactics = $this->tactics()->count();
        $completedTactics = $this->tactics()->where('status', 'completed')->count();

        return [
            'key_results' => [
                'total' => $totalKeyResults,
                'completed' => $completedKeyResults,
                'percentage' => $totalKeyResults > 0 ? round(($completedKeyResults / $totalKeyResults) * 100) : 0
            ],
            'tactics' => [
                'total' => $totalTactics,
                'completed' => $completedTactics,
                'percentage' => $totalTactics > 0 ? round(($completedTactics / $totalTactics) * 100) : 0
            ]
        ];
    }

    // Task Management Methods
    public function attachTask($task, int $contributionPercentage = 100): void
    {
        $this->tasks()->attach($task->id, [
            'contribution_percentage' => $contributionPercentage
        ]);
    }

    public function detachTask($task): void
    {
        $this->tasks()->detach($task->id);
    }

    public function updateTaskContribution($task, int $contributionPercentage): void
    {
        $this->tasks()->updateExistingPivot($task->id, [
            'contribution_percentage' => $contributionPercentage
        ]);
    }

    public function getTasksProgress(): array
    {
        $tasks = $this->tasks;

        if ($tasks->isEmpty()) {
            return [
                'total' => 0,
                'completed' => 0,
                'in_progress' => 0,
                'pending' => 0,
                'progress_percentage' => 0
            ];
        }

        $total = $tasks->count();
        $completed = $tasks->where('status', 'completed')->count();
        $inProgress = $tasks->where('status', 'in_progress')->count();
        $pending = $tasks->where('status', 'pending')->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'in_progress' => $inProgress,
            'pending' => $pending,
            'progress_percentage' => round(($completed / $total) * 100)
        ];
    }
}
